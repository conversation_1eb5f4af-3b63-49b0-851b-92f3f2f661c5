class UserModel {
  final String id;
  final String email;
  final String fullName;
  final UserPurpose purpose;
  final DateTime createdAt;
  final DateTime? lastSignIn;
  final String? avatarUrl;

  UserModel({
    required this.id,
    required this.email,
    required this.fullName,
    required this.purpose,
    required this.createdAt,
    this.lastSignIn,
    this.avatarUrl,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      purpose: UserPurpose.fromString(json['purpose'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastSignIn: json['last_sign_in'] != null 
          ? DateTime.parse(json['last_sign_in'] as String)
          : null,
      avatarUrl: json['avatar_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'purpose': purpose.value,
      'created_at': createdAt.toIso8601String(),
      'last_sign_in': lastSignIn?.toIso8601String(),
      'avatar_url': avatarUrl,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? fullName,
    UserPurpose? purpose,
    DateTime? createdAt,
    DateTime? lastSignIn,
    String? avatarUrl,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      purpose: purpose ?? this.purpose,
      createdAt: createdAt ?? this.createdAt,
      lastSignIn: lastSignIn ?? this.lastSignIn,
      avatarUrl: avatarUrl ?? this.avatarUrl,
    );
  }
}

enum UserPurpose {
  student('student'),
  customer('customer'),
  user('user');

  const UserPurpose(this.value);
  final String value;

  static UserPurpose fromString(String value) {
    switch (value.toLowerCase()) {
      case 'student':
        return UserPurpose.student;
      case 'customer':
        return UserPurpose.customer;
      case 'user':
        return UserPurpose.user;
      default:
        throw ArgumentError('Invalid user purpose: $value');
    }
  }

  String get displayName {
    switch (this) {
      case UserPurpose.student:
        return 'Student';
      case UserPurpose.customer:
        return 'Customer';
      case UserPurpose.user:
        return 'User';
    }
  }
}
