import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/custom_button.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/social_auth_buttons.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  UserPurpose? _selectedPurpose;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => context.go('/welcome'),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: size.height * 0.02),
              
              // Header
              _buildHeader(theme),
              
              const SizedBox(height: 32),
              
              // Sign Up Form
              _buildSignUpForm(),
              
              const SizedBox(height: 24),
              
              // Social Auth
              SocialAuthButtons(
                onGoogleSuccess: () => context.go('/dashboard'),
              ),
              
              const SizedBox(height: 32),
              
              // Sign In Link
              _buildSignInLink(context, theme),
              
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.createAccount,
          style: theme.textTheme.displayMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Join AquaFlow and start managing your water resources efficiently.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpForm() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        return Form(
          key: _formKey,
          child: Column(
            children: [
              // Full Name Field
              CustomTextField(
                label: AppStrings.fullName,
                controller: _nameController,
                validator: Validators.validateName,
                prefixIcon: Icons.person_outline,
              ),
              
              const SizedBox(height: 20),
              
              // Email Field
              CustomTextField(
                label: AppStrings.email,
                controller: _emailController,
                validator: Validators.validateEmail,
                isEmail: true,
                prefixIcon: Icons.email_outlined,
              ),
              
              const SizedBox(height: 20),
              
              // Password Field
              CustomTextField(
                label: AppStrings.password,
                controller: _passwordController,
                validator: Validators.validatePassword,
                isPassword: true,
                prefixIcon: Icons.lock_outline,
              ),
              
              const SizedBox(height: 20),
              
              // Confirm Password Field
              CustomTextField(
                label: AppStrings.confirmPassword,
                controller: _confirmPasswordController,
                validator: (value) => Validators.validateConfirmPassword(
                  value,
                  _passwordController.text,
                ),
                isPassword: true,
                prefixIcon: Icons.lock_outline,
              ),
              
              const SizedBox(height: 20),
              
              // Purpose Dropdown
              _buildPurposeDropdown(),
              
              const SizedBox(height: 32),
              
              // Sign Up Button
              CustomButton(
                text: AppStrings.signUp,
                onPressed: () => _handleSignUp(authProvider),
                isLoading: authProvider.isLoading,
                isFullWidth: true,
                type: ButtonType.primary,
              ),
              
              // Error Message
              if (authProvider.errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.error.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: AppColors.error, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          authProvider.errorMessage!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildPurposeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.purpose,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<UserPurpose>(
          value: _selectedPurpose,
          decoration: InputDecoration(
            hintText: AppStrings.selectPurpose,
            filled: true,
            fillColor: AppColors.surface,
            prefixIcon: const Icon(Icons.work_outline, color: AppColors.textTertiary, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.borderFocus, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          validator: (value) => Validators.validatePurpose(value?.value),
          items: UserPurpose.values.map((purpose) {
            return DropdownMenuItem<UserPurpose>(
              value: purpose,
              child: Text(purpose.displayName),
            );
          }).toList(),
          onChanged: (UserPurpose? value) {
            setState(() {
              _selectedPurpose = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSignInLink(BuildContext context, ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppStrings.alreadyHaveAccount,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () => context.go('/login'),
          child: Text(
            AppStrings.signInHere,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _handleSignUp(AuthProvider authProvider) async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_selectedPurpose == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(AppStrings.purposeRequired),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      final success = await authProvider.signUpWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        fullName: _nameController.text.trim(),
        purpose: _selectedPurpose!,
      );

      if (success && mounted) {
        context.go('/dashboard');
      }
    }
  }
}
