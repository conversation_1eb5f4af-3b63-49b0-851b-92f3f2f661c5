import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../providers/auth_provider.dart';

class SocialAuthButtons extends StatelessWidget {
  final VoidCallback? onGoogleSuccess;
  final VoidCallback? onGitHubSuccess;

  const SocialAuthButtons({
    super.key,
    this.onGoogleSuccess,
    this.onGitHubSuccess,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Divider with "Or continue with" text
        Row(
          children: [
            const Expanded(
              child: Divider(color: AppColors.border, thickness: 1),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                AppStrings.orContinueWith,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
            ),
            const Expanded(
              child: Divider(color: AppColors.border, thickness: 1),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Google Sign In Button
        Consumer<AuthProvider>(
          builder: (context, authProvider, _) {
            return _SocialButton(
              text: AppStrings.signInWithGoogle,
              icon: Icons.g_mobiledata, // Using built-in icon for now
              backgroundColor: Colors.white,
              textColor: AppColors.textPrimary,
              borderColor: AppColors.border,
              isLoading: authProvider.isLoading,
              onPressed: () async {
                final success = await authProvider.signInWithGoogle();
                if (success && onGoogleSuccess != null) {
                  onGoogleSuccess!();
                }
              },
            );
          },
        ),

        const SizedBox(height: 12),

        // GitHub Sign In Button (placeholder for now)
        _SocialButton(
          text: AppStrings.signInWithGitHub,
          icon: Icons.code, // Using built-in icon for now
          backgroundColor: const Color(0xFF24292e),
          textColor: Colors.white,
          borderColor: const Color(0xFF24292e),
          onPressed: () {
            // TODO: Implement GitHub sign in
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('GitHub sign in coming soon!'),
                backgroundColor: AppColors.primary,
              ),
            );
          },
        ),
      ],
    );
  }
}

class _SocialButton extends StatelessWidget {
  final String text;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;
  final Color borderColor;
  final VoidCallback? onPressed;
  final bool isLoading;

  const _SocialButton({
    required this.text,
    required this.icon,
    required this.backgroundColor,
    required this.textColor,
    required this.borderColor,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: textColor,
          side: BorderSide(color: borderColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
        child:
            isLoading
                ? SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(textColor),
                  ),
                )
                : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(icon, size: 20, color: textColor),
                    const SizedBox(width: 12),
                    Text(
                      text,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
