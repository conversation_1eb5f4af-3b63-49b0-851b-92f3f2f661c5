class AppStrings {
  // App Info
  static const String appName = 'AquaFlow';
  static const String appTagline = 'Professional Water Management Solution';
  static const String appDescription = 'Streamline your water management with our comprehensive platform designed for students, customers, and professionals.';
  
  // Welcome Screen
  static const String welcomeTitle = 'Welcome to AquaFlow';
  static const String welcomeSubtitle = 'Your trusted partner in water management';
  static const String getStarted = 'Get Started';
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String signInHere = 'Sign in here';
  
  // Authentication
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String createAccount = 'Create Account';
  static const String forgotPassword = 'Forgot Password?';
  static const String dontHaveAccount = "Don't have an account?";
  static const String signUpHere = 'Sign up here';
  static const String orContinueWith = 'Or continue with';
  static const String signInWithGoogle = 'Continue with Google';
  static const String signInWithGitHub = 'Continue with GitHub';
  
  // Form Fields
  static const String fullName = 'Full Name';
  static const String email = 'Email Address';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String purpose = 'Purpose';
  static const String selectPurpose = 'Select your purpose';
  
  // Purpose Options
  static const String student = 'Student';
  static const String customer = 'Customer';
  static const String user = 'User';
  
  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort = 'Password must be at least 8 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
  static const String nameRequired = 'Full name is required';
  static const String purposeRequired = 'Please select your purpose';
  
  // Success Messages
  static const String accountCreated = 'Account created successfully!';
  static const String signInSuccessful = 'Signed in successfully!';
  static const String passwordResetSent = 'Password reset email sent!';
  
  // Error Messages
  static const String signInError = 'Failed to sign in. Please try again.';
  static const String signUpError = 'Failed to create account. Please try again.';
  static const String networkError = 'Network error. Please check your connection.';
  static const String unknownError = 'An unexpected error occurred.';
  
  // Dashboard
  static const String dashboard = 'Dashboard';
  static const String waterUsage = 'Water Usage';
  static const String analytics = 'Analytics';
  static const String reports = 'Reports';
  static const String settings = 'Settings';
  static const String profile = 'Profile';
  static const String logout = 'Logout';
  
  // General
  static const String loading = 'Loading...';
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String edit = 'Edit';
  static const String delete = 'Delete';
  static const String confirm = 'Confirm';
}
