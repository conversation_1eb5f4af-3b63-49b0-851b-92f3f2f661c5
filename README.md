# AquaFlow - Professional Water Management App

A professional Flutter application for water management designed for students, customers, and users. Built with Supabase backend integration and modern UI/UX principles.

## Features

- **Professional Authentication System**
  - Email/Password authentication
  - Google Sign-In integration
  - GitHub Sign-In (coming soon)
  - User role selection (Student, Customer, User)

- **Modern UI/UX**
  - Professional water-themed design
  - Responsive layout
  - Material Design 3
  - Custom color scheme and typography

- **Water Management Dashboard**
  - Usage monitoring
  - Analytics and reports
  - User profile management

## Setup Instructions

### 1. Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / VS Code
- Supabase account

### 2. Supabase Setup
1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and anon key
3. Update `lib/main.dart` with your Supabase credentials:
   ```dart
   await Supabase.initialize(
     url: 'YOUR_SUPABASE_URL',
     anonKey: 'YOUR_SUPABASE_ANON_KEY',
   );
   ```

### 3. Database Schema
Create the following table in your Supabase database:

```sql
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT NOT NULL,
  purpose TEXT NOT NULL CHECK (purpose IN ('student', 'customer', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_sign_in TIMESTAMP WITH TIME ZONE,
  avatar_url TEXT
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);
```

### 4. Google Sign-In Setup (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable Google Sign-In API
4. Configure OAuth consent screen
5. Create OAuth 2.0 credentials
6. Add the credentials to your Supabase project

### 5. Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── app.dart                  # Main app configuration
├── core/
│   ├── constants/           # App constants (colors, strings, themes)
│   ├── services/            # Backend services
│   └── utils/               # Utility functions
├── features/
│   ├── auth/               # Authentication feature
│   │   ├── models/         # User models
│   │   ├── providers/      # State management
│   │   ├── screens/        # Auth screens
│   │   └── widgets/        # Auth widgets
│   └── dashboard/          # Dashboard feature
└── shared/
    └── widgets/            # Reusable widgets
```

## Technologies Used

- **Flutter** - Cross-platform mobile framework
- **Supabase** - Backend as a Service
- **Provider** - State management
- **GoRouter** - Navigation
- **Google Fonts** - Typography
- **Google Sign-In** - Social authentication

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
